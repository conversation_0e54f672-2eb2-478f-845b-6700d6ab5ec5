{"name": "website", "version": "1.0.0", "description": "Website template for Payload", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "dev:prod": "cross-env NODE_OPTIONS=--no-deprecation rm -rf .next && pnpm build && pnpm start", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "ii": "cross-env NODE_OPTIONS=--no-deprecation pnpm --ignore-workspace install", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "lint:fix": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "reinstall": "cross-env NODE_OPTIONS=--no-deprecation rm -rf node_modules && rm pnpm-lock.yaml && pnpm --ignore-workspace install", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "test": "pnpm run test:int && pnpm run test:e2e", "test:e2e": "cross-env NODE_OPTIONS=\"--no-deprecation --no-experimental-strip-types\" pnpm exec playwright test --config=playwright.config.ts", "test:int": "cross-env NODE_OPTIONS=--no-deprecation vitest run --config ./vitest.config.mts"}, "dependencies": {"@payloadcms/admin-bar": "3.48.0", "@payloadcms/db-postgres": "3.48.0", "@payloadcms/live-preview-react": "3.48.0", "@payloadcms/next": "3.48.0", "@payloadcms/payload-cloud": "3.48.0", "@payloadcms/plugin-form-builder": "3.48.0", "@payloadcms/plugin-nested-docs": "3.48.0", "@payloadcms/plugin-redirects": "3.48.0", "@payloadcms/plugin-search": "3.48.0", "@payloadcms/plugin-seo": "3.48.0", "@payloadcms/richtext-lexical": "3.48.0", "@payloadcms/ui": "3.48.0", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cross-env": "^7.0.3", "dotenv": "16.4.7", "framer-motion": "^12.23.12", "geist": "^1.3.0", "graphql": "^16.8.2", "lucide-react": "^0.378.0", "next": "15.3.3", "next-sitemap": "^4.2.3", "payload": "3.48.0", "prism-react-renderer": "^2.3.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.45.4", "sharp": "0.34.2", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.50.0", "@tailwindcss/typography": "^0.5.13", "@testing-library/react": "16.3.0", "@types/escape-html": "^1.0.2", "@types/node": "22.5.4", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "@vitejs/plugin-react": "4.5.2", "autoprefixer": "^10.4.19", "copyfiles": "^2.4.1", "eslint": "^9.16.0", "eslint-config-next": "15.3.0", "jsdom": "26.1.0", "playwright": "1.50.0", "playwright-core": "1.50.0", "postcss": "^8.4.38", "prettier": "^3.4.2", "tailwindcss": "^3.4.3", "typescript": "5.7.3", "vite-tsconfig-paths": "5.1.4", "vitest": "3.2.3"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp", "esbuild", "unrs-resolver"]}}