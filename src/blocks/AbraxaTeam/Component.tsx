import { cn } from '@/utilities/ui'
import React from 'react'
import RichText from '@/components/RichText'

import type { AbraxaTeamBlock as AbraxaTeamBlockProps } from '@/payload-types'
import { Media } from '@/components/Media'

export const AbraxaTeamBlock: React.FC<AbraxaTeamBlockProps> = ({
  layout = 'horizontal',
  title,
  items,
  className,
}) => {
  if (layout === 'horizontal') {
    return (
      <div className="flex items-center min-h-screen" data-theme="dark">
        <div className={cn('container py-20', className)}>
          {title && (
            <div className="mb-20 md:mb-28 xl:text-center">
              <RichText data={title} enableGutter={false} />
            </div>
          )}
          <div className="grid grid-cols-12 gap-x-8 gap-y-20 items-center">
            {items?.map((item, index) => (
              <div
                key={index}
                className="col-span-12 gap-6 md:gap-12 flex items-start xl:col-span-4 xl:flex-col xl:items-center"
              >
                <div className="flex-shrink-0">
                  {item.media && typeof item.media === 'object' && (
                    <Media className="size-24 md:size-40" imgClassName="" resource={item.media} />
                  )}
                </div>
                <div className="flex-1 xl:text-center payload-richtext prose md:prose-md dark:prose-invert">
                  <h3 className="text-primary-foreground">{item.name}</h3>
                  <div className="text-primary-foreground mt-1 mb-4 text-base">{item.position}</div>
                  {item.description && (
                    <RichText
                      data={item.description}
                      className="text-tertiary-foreground"
                      enableGutter={false}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Vertical layout (Executive Team style)
  return (
    <div className={cn('container py-20', className)} data-theme="dark">
      <div className="grid grid-cols-12 gap-x-8 gap-y-20 items-center">
        <div className="col-span-12 xl:col-span-3 xl:sticky xl:top-8">
          {title && <RichText data={title} enableGutter={false} />}
        </div>
        <div className="col-span-12 xl:col-span-7 xl:col-end-13 space-y-20">
          {items?.map((item, index) => (
            <div key={index} className="flex gap-6 md:gap-12 items-start">
              <div className="flex-shrink-0">
                {item.media && typeof item.media === 'object' && (
                  <Media className="size-24 md:size-40" imgClassName="" resource={item.media} />
                )}
              </div>
              <div className="flex-1 payload-richtext prose md:prose-md dark:prose-invert">
                <h3 className="text-primary-foreground">{item.name}</h3>
                <div className="text-primary-foreground mt-1 mb-4 text-base">{item.position}</div>
                {item.description && (
                  <RichText
                    className="text-tertiary-foreground"
                    data={item.description}
                    enableGutter={false}
                  />
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
