import type { Block } from 'payload'

import {
  FixedToolbarFeature,
  HeadingFeature,
  InlineToolbarFeature,
  lexicalEditor,
} from '@payloadcms/richtext-lexical'

export const AbraxaSlider: Block = {
  slug: 'abraxaSlider',
  interfaceName: 'AbraxaSliderBlock',
  fields: [
    {
      name: 'title',
      type: 'richText',
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [
            ...rootFeatures,
            HeadingFeature({ enabledHeadingSizes: ['h2', 'h3', 'h4'] }),
            FixedToolbarFeature(),
            InlineToolbarFeature(),
          ]
        },
      }),
      label: 'Section Title',
      required: false,
    },
    {
      name: 'slides',
      type: 'array',
      admin: {
        initCollapsed: true,
      },
      minRows: 1,
      fields: [
        {
          name: 'statistic',
          type: 'text',
          label: 'Statistic (e.g., "40+ hours")',
          required: true,
        },
        {
          name: 'statisticSubtext',
          type: 'text',
          label: 'Statistic Subtext (e.g., "saved a week")',
          required: false,
        },
        {
          name: 'testimonial',
          type: 'richText',
          editor: lexicalEditor({
            features: ({ rootFeatures }) => {
              return [
                ...rootFeatures,
                HeadingFeature({ enabledHeadingSizes: ['h4', 'h5', 'h6'] }),
                FixedToolbarFeature(),
                InlineToolbarFeature(),
              ]
            },
          }),
          label: 'Testimonial Text',
          required: true,
        },
        {
          name: 'authorName',
          type: 'text',
          label: 'Author Name',
          required: true,
        },
        {
          name: 'authorTitle',
          type: 'text',
          label: 'Author Title/Position',
          required: false,
        },
        {
          name: 'companyLogo',
          type: 'upload',
          relationTo: 'media',
          label: 'Company Logo',
          required: false,
        },
        {
          name: 'backgroundColor',
          type: 'select',
          label: 'Background Color',
          defaultValue: 'dark',
          options: [
            {
              label: 'Dark',
              value: 'dark',
            },
            {
              label: 'Light',
              value: 'light',
            },
            {
              label: 'Primary',
              value: 'primary',
            },
          ],
        },
      ],
    },
    {
      name: 'className',
      label: 'Additional CSS Classes',
      type: 'text',
      required: false,
    },
  ],
  labels: {
    plural: 'Abraxa Sliders',
    singular: 'Abraxa Slider',
  },
}
