'use client'

import { cn } from '@/utilities/ui'
import React, { useRef, useState, useEffect } from 'react'
import RichText from '@/components/RichText'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight } from 'lucide-react'

import type { AbraxaSliderBlock as AbraxaSliderBlockProps } from '@/payload-types'

import { Media } from '@/components/Media'

export const AbraxaSliderBlock: React.FC<AbraxaSliderBlockProps> = (props) => {
  const { slides, title, className } = props
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)

  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current
      setCanScrollLeft(scrollLeft > 0)
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1)
    }
  }

  useEffect(() => {
    checkScrollButtons()
    const container = scrollContainerRef.current
    if (container) {
      container.addEventListener('scroll', checkScrollButtons)
      return () => container.removeEventListener('scroll', checkScrollButtons)
    }
  }, [])

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      const slideWidth = scrollContainerRef.current.children[0]?.clientWidth || 320
      const gap = window.innerWidth >= 768 ? 32 : 16 // md breakpoint gap
      scrollContainerRef.current.scrollBy({
        left: -slideWidth - gap,
        behavior: 'smooth',
      })
    }
  }

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      const slideWidth = scrollContainerRef.current.children[0]?.clientWidth || 320
      const gap = window.innerWidth >= 768 ? 32 : 16 // md breakpoint gap
      scrollContainerRef.current.scrollBy({
        left: slideWidth + gap,
        behavior: 'smooth',
      })
    }
  }

  const getBackgroundClasses = (bgColor: string) => {
    switch (bgColor) {
      case 'light':
        return 'bg-background text-foreground'
      case 'primary':
        return 'bg-primary text-primary-foreground'
      case 'dark':
      default:
        return 'bg-slate-900 text-white'
    }
  }

  if (!slides || slides.length === 0) {
    return null
  }

  return (
    <div className={cn('py-20', className)}>
      <div className="grid gap-10 slider">
        {title && (
          <div className="container mb-10">
            <div className="grid gap-x-8 grid-cols-12">
              <RichText data={title} className="col-span-12 xl:col-span-7" enableGutter={false} />
            </div>
          </div>
        )}
        {/* Slides Container */}
        <div
          ref={scrollContainerRef}
          className="flex gap-4 md:gap-8 overflow-x-auto [&::-webkit-scrollbar]:hidden slider-track"
          style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
          }}
        >
          {slides.map((slide, index) => (
            <div
              key={index}
              className={cn(
                'flex-none w-full max-w-screen-sm xl:max-w-4xl AbraxaCard grid gap-y-4 xl:gap-y-6 p-10 rounded-3xl',
                getBackgroundClasses(slide.backgroundColor || 'dark'),
              )}
            >
              {/* Statistic */}
              <div className="prose md:prose-md dark:prose-invert">
                <h3 className="text-gradient-primary">{slide.statistic}</h3>
                {slide.statisticSubtext && (
                  <p className="text-sm opacity-60">{slide.statisticSubtext}</p>
                )}
              </div>

              {/* Testimonial */}
              <div className="text-base xl:text-2xl text-secondary-foreground">
                <RichText
                  data={slide.testimonial}
                  enableGutter={false}
                  enableProse={false}
                  className="leading-relaxed"
                />
              </div>

              {/* Author Info */}
              <div className="mb-4 xl:mb-10">
                <p className="font-semibold text-lg">{slide.authorName}</p>
                {slide.authorTitle && <p className="text-sm opacity-60">{slide.authorTitle}</p>}
              </div>

              {/* Company Logo */}
              {slide.companyLogo && typeof slide.companyLogo === 'object' && (
                <div className="flex items-center">
                  <Media
                    resource={slide.companyLogo}
                    imgClassName="h-12 w-auto opacity-60"
                    priority={false}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
        {/* Navigation Buttons */}
        <div className="container">
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={scrollLeft}
              disabled={!canScrollLeft}
              className={cn(
                'h-12 w-12 rounded-full bg-background/80 backdrop-blur-sm border border-border hover:bg-background',
                {
                  'opacity-50 cursor-not-allowed': !canScrollLeft,
                },
              )}
            >
              <ChevronLeft className="h-5 w-5" />
              <span className="sr-only">Previous slide</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={scrollRight}
              disabled={!canScrollRight}
              className={cn(
                'h-12 w-12 rounded-full bg-background/80 backdrop-blur-sm border border-border hover:bg-background',
                {
                  'opacity-50 cursor-not-allowed': !canScrollRight,
                },
              )}
            >
              <ChevronRight className="h-5 w-5" />
              <span className="sr-only">Next slide</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
