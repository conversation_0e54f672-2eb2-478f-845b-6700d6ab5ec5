import { cn } from '@/utilities/ui'
import React from 'react'
import RichText from '@/components/RichText'

import type { ContentBlock as ContentBlockProps } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import { Media } from '@/components/Media'

export const ContentBlock: React.FC<ContentBlockProps> = (props) => {
  const { columns, className } = props

  const colsSpanClasses = {
    full: '12',
    half: '6',
    oneFourth: '3',
    oneThird: '4',
    twoThirds: '8',
    fiveTwelfths: '5',
    sevenTwelfths: '7',
  }

  return (
    <div className={cn('container py-20', className)}>
      <div className="grid grid-cols-12 gap-y-8 gap-x-8">
        {columns &&
          columns.length > 0 &&
          columns.map((col, index) => {
            const { enableLink, link, richText, size, media, className } = col

            return (
              <div
                className={cn(`col-span-12 lg:col-span-${colsSpanClasses[size!]}`, className, {
                  'md:col-span-12': size !== 'full',
                })}
                key={index}
              >
                {richText && <RichText data={richText} enableGutter={false} />}

                {media && typeof media === 'object' && (
                  <Media imgClassName="" priority resource={media} />
                )}
                <div className="mt-8">{enableLink && <CMSLink size="lg" {...link} />}</div>
              </div>
            )
          })}
      </div>
    </div>
  )
}
