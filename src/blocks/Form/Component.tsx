'use client'
import type { FormFieldBlock, Form as FormType } from '@payloadcms/plugin-form-builder/types'

import { useRouter } from 'next/navigation'
import React, { useCallback, useState } from 'react'
import { useForm, FormProvider } from 'react-hook-form'
import RichText from '@/components/RichText'
import { Button } from '@/components/ui/button'
import type { SerializedEditorState } from '@payloadcms/richtext-lexical/lexical'

import { fields } from './fields'
import { getClientSideURL } from '@/utilities/getURL'

import { CMSLink } from '@/components/Link'
import { Linkedin, Mail, MapPin } from 'lucide-react'
import { Separator } from '@/components/ui/separator'
import { Media } from '@/components/Media'

export type FormBlockType = {
  blockName?: string
  blockType?: 'formBlock'
  enableIntro: boolean
  email?: string
  linkedIn?: string
  address?: string
  testimonial?: string
  companyLogo?: string
  form: FormType
  introContent?: SerializedEditorState
}

export const FormBlock: React.FC<
  {
    id?: string
  } & FormBlockType
> = (props) => {
  const {
    enableIntro,
    form: formFromProps,
    form: { id: formID, confirmationMessage, confirmationType, redirect, submitButtonLabel } = {},
    introContent,
    email,
    linkedIn,
    address,
    testimonial,
    companyLogo,
  } = props

  const formMethods = useForm({
    defaultValues: formFromProps.fields,
  })
  const {
    control,
    formState: { errors },
    handleSubmit,
    register,
  } = formMethods

  const [isLoading, setIsLoading] = useState(false)
  const [hasSubmitted, setHasSubmitted] = useState<boolean>()
  const [error, setError] = useState<{ message: string; status?: string } | undefined>()
  const router = useRouter()

  const onSubmit = useCallback(
    (data: FormFieldBlock[]) => {
      let loadingTimerID: ReturnType<typeof setTimeout>
      const submitForm = async () => {
        setError(undefined)

        const dataToSend = Object.entries(data).map(([name, value]) => ({
          field: name,
          value,
        }))

        // delay loading indicator by 1s
        loadingTimerID = setTimeout(() => {
          setIsLoading(true)
        }, 1000)

        try {
          const req = await fetch(`${getClientSideURL()}/api/form-submissions`, {
            body: JSON.stringify({
              form: formID,
              submissionData: dataToSend,
            }),
            headers: {
              'Content-Type': 'application/json',
            },
            method: 'POST',
          })

          const res = await req.json()

          clearTimeout(loadingTimerID)

          if (req.status >= 400) {
            setIsLoading(false)

            setError({
              message: res.errors?.[0]?.message || 'Internal Server Error',
              status: res.status,
            })

            return
          }

          setIsLoading(false)
          setHasSubmitted(true)

          if (confirmationType === 'redirect' && redirect) {
            const { url } = redirect

            const redirectUrl = url

            if (redirectUrl) router.push(redirectUrl)
          }
        } catch (err) {
          console.warn(err)
          setIsLoading(false)
          setError({
            message: 'Something went wrong.',
          })
        }
      }

      void submitForm()
    },
    [router, formID, redirect, confirmationType],
  )

  return (
    <div className="container py-20 relative">
      <div className="grid grid-cols-12 gap-x-8 gap-y-20 items-center">
        <div className="col-span-12 xl:col-span-6 2xl:col-span-5">
          {enableIntro && introContent && !hasSubmitted && (
            <RichText data={introContent} enableGutter={false} />
          )}
          <div className="flex flex-col gap-6">
            <Separator className="bg-secondary" />
            {email && (
              <div className="flex gap-2 items-center text-secondary-foreground">
                <Mail className="size-5" />
                <CMSLink
                  className="hover:text-primary-foreground"
                  appearance="inline"
                  label={email}
                  newTab={true}
                  reference={null}
                  size="default"
                  type="custom"
                  url={`mailto:${email}`}
                />
              </div>
            )}
            {linkedIn && (
              <div className="flex gap-2 items-center text-secondary-foreground">
                <Linkedin className="size-5" />
                <CMSLink
                  className="hover:text-primary-foreground"
                  appearance="inline"
                  label={linkedIn}
                  newTab={true}
                  reference={null}
                  size="default"
                  type="custom"
                  url="https://www.linkedin.com/company/abraxa-group/"
                />
              </div>
            )}
            {address && (
              <div className="flex gap-2 items-center text-secondary-foreground">
                <MapPin className="size-5" />
                {address}
              </div>
            )}
            <Separator className="bg-secondary" />
            {testimonial && (
              <div className="flex gap-4 items-stretch text-tertiary-foreground text-sm">
                <div className="bg-secondary w-1 rounded"></div>
                <div className="flex-1">{testimonial}</div>
              </div>
            )}
            {companyLogo && (
              <Media imgClassName="h-12 w-auto opacity-60" priority resource={companyLogo} />
            )}
          </div>
        </div>
        <div className="col-span-12 lg:col-span-5 xl:col-end-13">
          <FormProvider {...formMethods}>
            {!isLoading && hasSubmitted && confirmationType === 'message' && (
              <div className="AbraxaContact_success">
                <RichText data={confirmationMessage} />
                <CMSLink
                  appearance="default"
                  size="lg"
                  label="Back to home"
                  type="custom"
                  url="/"
                />
              </div>
            )}
            {isLoading && !hasSubmitted && (
              <div className="px-10 py-8 text-tertiary-foreground">Loading, please wait...</div>
            )}
            {error && (
              <div className="px-10 py-8 text-destructive-foreground">{`${error.status || '500'}: ${error.message || ''}`}</div>
            )}
            {!hasSubmitted && (
              <div className="AbraxaCard border border-border rounded-lg">
                <div className="text-lg font-medium px-10 pt-8">Drop us a line</div>
                <form id={formID} onSubmit={handleSubmit(onSubmit)}>
                  <div className="px-10 py-6">
                    {formFromProps &&
                      formFromProps.fields &&
                      formFromProps.fields?.map((field, index) => {
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        const Field: React.FC<any> =
                          fields?.[field.blockType as keyof typeof fields]
                        if (Field) {
                          return (
                            <div className="mb-6 last:mb-0" key={index}>
                              <Field
                                form={formFromProps}
                                {...field}
                                {...formMethods}
                                control={control}
                                errors={errors}
                                register={register}
                              />
                            </div>
                          )
                        }
                        return null
                      })}
                  </div>
                  <div className="px-10 pb-8">
                    <Button form={formID} type="submit" variant="default" className="w-full">
                      {submitButtonLabel}
                    </Button>
                  </div>
                </form>
              </div>
            )}
          </FormProvider>
        </div>
      </div>
    </div>
  )
}
