import React from 'react'

import type { AbraxaCTABlock as AbraxaCTABlockProps } from '@/payload-types'

import RichText from '@/components/RichText'
import { CMSLink } from '@/components/Link'

export const AbraxaCTABlock: React.FC<AbraxaCTABlockProps> = ({ links, richText }) => {
  return (
    <div className="container py-20">
      <div className="grid lg:grid-cols-12 gap-y-10 xl:gap-y-20">
        <div className="col-span-12 lg:col-span-8 lg:col-start-3 text-center">
          {richText && <RichText data={richText} enableGutter={false} />}
        </div>
        <div className="flex flex-col gap-8 items-center col-span-12">
          {(links || []).map(({ link }, i) => {
            return <CMSLink key={i} size="lg" {...link} />
          })}
        </div>
        <div className="AbraxaLogo_container col-span-12">
          <div className="AbraxaLogo_mask">
            <div className="AbraxaLogo_noise"></div>
            <div className="AbraxaLogo_gradient"></div>
          </div>
        </div>
      </div>
    </div>
  )
}
