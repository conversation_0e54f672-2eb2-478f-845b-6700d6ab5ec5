import { cn } from '@/utilities/ui'
import React from 'react'
import RichText from '@/components/RichText'

import type { AbraxaPartnersBlock as AbraxaPartnersBlockProps } from '@/payload-types'

import { Media } from '@/components/Media'

export const AbraxaPartnersBlock: React.FC<AbraxaPartnersBlockProps> = ({
  richText,
  media,
  media1,
  media2,
  media3,
  className,
}) => {
  return (
    <div className={cn('container pb-20 pt-10', className)}>
      <div className="grid grid-cols-12 gap-y-8 gap-x-8 items-center">
        <div className="col-span-12 text-center xl:col-span-4 xl:text-left">
          {richText && <RichText data={richText} enableGutter={false} />}
        </div>
        <div className="col-span-12 xl:col-span-7 xl:col-end-13">
          <div className="flex justify-between gap-8">
            {media && typeof media === 'object' && (
              <Media imgClassName="" priority resource={media} />
            )}

            {media1 && typeof media1 === 'object' && (
              <Media imgClassName="" priority resource={media1} />
            )}

            {media2 && typeof media2 === 'object' && (
              <Media imgClassName="" priority resource={media2} />
            )}

            {media3 && typeof media3 === 'object' && (
              <Media imgClassName="" priority resource={media3} />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
