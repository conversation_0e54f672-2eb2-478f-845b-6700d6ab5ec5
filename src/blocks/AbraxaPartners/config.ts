import type { Block } from 'payload'

import {
  FixedToolbarFeature,
  HeadingFeature,
  InlineToolbarFeature,
  lexicalEditor,
} from '@payloadcms/richtext-lexical'

export const AbraxaPartners: Block = {
  slug: 'abraxaPartners',
  interfaceName: 'AbraxaPartnersBlock',
  fields: [
    {
      name: 'richText',
      type: 'richText',
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [
            ...rootFeatures,
            HeadingFeature({ enabledHeadingSizes: ['h2', 'h3', 'h4'] }),
            FixedToolbarFeature(),
            InlineToolbarFeature(),
          ]
        },
      }),
      label: false,
    },
    {
      name: 'media',
      type: 'upload',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'media1',
      type: 'upload',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'media2',
      type: 'upload',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'media3',
      type: 'upload',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'className',
      label: 'Class',
      type: 'text',
      required: false,
    },
  ],
  labels: {
    plural: 'Abraxa Partners',
    singular: 'Abraxa Partner',
  },
}
