import type { Block, Field } from 'payload'

import {
  FixedToolbarFeature,
  HeadingFeature,
  InlineToolbarFeature,
  lexicalEditor,
} from '@payloadcms/richtext-lexical'

const columnFields: Field[] = [
  {
    name: 'size',
    type: 'select',
    defaultValue: 'oneThird',
    options: [
      {
        label: 'One Fourth',
        value: 'oneFourth',
      },
      {
        label: 'Five Twelfths',
        value: 'fiveTwelfths',
      },
      {
        label: 'One Third',
        value: 'oneThird',
      },
      {
        label: 'Half',
        value: 'half',
      },
      {
        label: 'Seven Twelfths',
        value: 'sevenTwelfths',
      },
      {
        label: 'Two Thirds',
        value: 'twoThirds',
      },
      {
        label: 'Full',
        value: 'full',
      },
    ],
  },
  {
    name: 'richText',
    type: 'richText',
    editor: lexicalEditor({
      features: ({ rootFeatures }) => {
        return [
          ...rootFeatures,
          HeadingFeature({ enabledHeadingSizes: ['h2', 'h3', 'h4'] }),
          FixedToolbarFeature(),
          InlineToolbarFeature(),
        ]
      },
    }),
    label: false,
  },
  {
    name: 'items',
    type: 'array',
    admin: {
      initCollapsed: true,
    },
    fields: [
      {
        name: 'title',
        type: 'text',
        required: false,
      },
      {
        name: 'description',
        type: 'text',
        required: false,
      },
      {
        name: 'media',
        type: 'upload',
        relationTo: 'media',
        required: false,
      },
    ],
  },
  {
    name: 'className',
    label: 'Class',
    type: 'text',
    required: false,
  },
]

export const AbraxaSecurity: Block = {
  slug: 'abraxaSecurity',
  interfaceName: 'AbraxaSecurityBlock',
  fields: [
    {
      name: 'columns',
      type: 'array',
      admin: {
        initCollapsed: true,
      },
      fields: columnFields,
    },
    {
      name: 'className',
      label: 'Class',
      type: 'text',
      required: false,
    },
  ],
}
