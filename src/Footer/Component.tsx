import { getCachedGlobal } from '@/utilities/getGlobals'
import Link from 'next/link'
import Image from 'next/image'
import React from 'react'

import type { Footer } from '@/payload-types'

// import { ThemeSelector } from '@/providers/Theme/ThemeSelector'

import { CMSLink } from '@/components/Link'
import { Logo } from '@/components/Logo/Logo'

export async function Footer() {
  const footerData: Footer = await getCachedGlobal('footer', 1)()

  const navItems = footerData?.navItems || []
  const secondaryNavItems = footerData?.secondaryNavItems || []

  return (
    <footer className="mt-auto">
      <div className="container py-20 flex flex-col gap-8 border-t border-tertiary">
        <div className="grid grid-cols-12 gap-x-8">
          <Link className="col-span-2 flex  items-start" href="/">
            <Logo className="opacity-75 h-4 w-[121px] max-w-fit" />
          </Link>

          <nav className="col-span-10 flex flex-col md:flex-row gap-4">
            <div className="flex-1 flex items-center gap-8">
              {navItems.map(({ link }, i) => {
                return (
                  <CMSLink
                    className="text-tertiary-foreground text-sm hover:text-secondary-foreground focus:text-tertiary-foreground"
                    key={i}
                    {...link}
                  />
                )
              })}
            </div>
            {footerData.linkedIn && (
              <div className="flex gap-2 items-center justify-end">
                <CMSLink
                  className="text-tertiary-foreground text-sm hover:text-secondary-foreground focus:text-tertiary-foreground"
                  label="LinkedIn"
                  newTab={true}
                  reference={null}
                  size="default"
                  type="custom"
                  url={footerData.linkedIn}
                />
                <Image
                  src="/api/media/file/Logo_LinkedIn.svg"
                  alt="LinkedIn"
                  width={18}
                  height={18}
                />
              </div>
            )}
          </nav>
        </div>
        <div className="flex items-center space-between ">
          <div className="text-xs text-quaternary-foreground flex-1">
            Copyright {new Date().getFullYear()} @ Abraxa Group. All rights reserved.
          </div>
          <nav className="flex items-center justify-end gap-4">
            {secondaryNavItems.map(({ link }, i) => {
              return (
                <CMSLink
                  className="text-quaternary-foreground text-xs hover:text-tertiary-foreground focus:text-tertiary-foreground"
                  key={i}
                  {...link}
                />
              )
            })}
          </nav>
        </div>
      </div>
    </footer>
  )
}
